# RSI Divergence Strategy for Hummingbot

## Overview

This strategy is a Python implementation of a TradingView PineScript RSI divergence detector, converted to work with the Hummingbot trading framework. It identifies divergences between price action and the RSI (Relative Strength Index) indicator to generate trading signals.

## How It Works

### Divergence Detection

The strategy detects two types of divergences:

1. **Bearish Divergence (Sell Signal)**
   - Price makes higher highs
   - RSI makes lower highs
   - Indicates potential downward price movement

2. **Bullish Divergence (Buy Signal)**
   - Price makes lower lows
   - RSI makes higher lows
   - Indicates potential upward price movement

### Technical Implementation

The strategy follows the original PineScript logic:
- Uses a lookback period to detect high and low points
- Calculates RSI with a configurable period (default: 14)
- Compares price ratios with RSI ratios to identify divergences
- Filters signals based on divergence strength and timing

## Files

- `rsi_divergence_strategy.py` - Main strategy implementation
- `rsi_divergence_config_example.py` - Configuration examples and parameter guidelines
- `RSI_DIVERGENCE_README.md` - This documentation file

## Quick Start

### 1. Basic Usage

```python
# Copy the strategy file to your Hummingbot scripts directory
# Run with default parameters:
from scripts.community.rsi_divergence_strategy import RSIDivergenceStrategy

# The strategy will use default parameters:
# - Trading pair: BTC-USDT
# - Exchange: Binance
# - Lookback period: 10
# - RSI period: 14
# - Order amount: $50
```

### 2. Custom Configuration

```python
# Create your own configuration by copying the example:
from scripts.community.rsi_divergence_config_example import MyRSIDivergenceStrategy

# Modify parameters in the config file to suit your needs
```

### 3. Running the Strategy

1. Start Hummingbot
2. Use the `start` command with your strategy file
3. Monitor the strategy status for divergence signals

## Configuration Parameters

### Core Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `trading_pair` | "BTC-USDT" | Trading pair to monitor |
| `exchange` | "binance" | Exchange to trade on |
| `order_amount_usd` | 50 | Order size in USD |
| `lookback_period` | 10 | Period for high/low detection |
| `rsi_period` | 14 | RSI calculation period |
| `min_divergence_strength` | 0.02 | Minimum divergence threshold |

### Risk Management

| Parameter | Default | Description |
|-----------|---------|-------------|
| `stop_loss` | 0.01 | Stop loss percentage (1%) |
| `take_profit` | 0.02 | Take profit percentage (2%) |
| `time_limit` | 3600 | Max position time (seconds) |
| `trailing_stop_activation_delta` | 0.005 | Trailing stop activation |
| `trailing_stop_trailing_delta` | 0.002 | Trailing stop distance |

## Strategy Configurations

The package includes several pre-configured strategy variants:

### 1. Conservative Configuration
- Higher divergence threshold (0.03)
- Tighter risk management (1% SL, 2% TP)
- Longer timeframe (15m candles)
- Suitable for: Risk-averse traders

### 2. Aggressive Configuration
- Lower divergence threshold (0.015)
- Wider risk management (2% SL, 4% TP)
- Shorter timeframe (3m candles)
- Suitable for: Active day traders

### 3. Scalping Configuration
- Very low threshold (0.01)
- Quick targets (0.5% SL, 1% TP)
- 1-minute candles
- Suitable for: High-frequency scalpers

## Parameter Guidelines

### Lookback Period
- **5-8**: More sensitive, more signals, higher false positive rate
- **10-15**: Balanced approach, good for most market conditions
- **20-30**: Less sensitive, fewer but more reliable signals

### RSI Period
- **7-10**: More responsive to price changes, noisier
- **14**: Standard setting, good balance
- **21-30**: Smoother signals, less responsive

### Divergence Strength
- **0.01-0.02**: Aggressive, more signals
- **0.02-0.03**: Balanced approach
- **0.03-0.05**: Conservative, fewer but stronger signals

### Timeframes
- **1m, 3m**: Scalping and high-frequency trading
- **5m, 15m**: Day trading
- **30m, 1h**: Swing trading
- **4h, 1d**: Position trading

## Risk Management Best Practices

1. **Always use stop losses** - The strategy includes automatic stop loss functionality
2. **Position sizing** - Start with small amounts while learning the strategy
3. **Market conditions** - Divergence strategies work best in trending markets
4. **Backtesting** - Test parameters on historical data before live trading
5. **Monitoring** - Regularly check strategy performance and adjust parameters

## Troubleshooting

### Common Issues

1. **No signals generated**
   - Check if enough historical data is available
   - Reduce `min_divergence_strength` for more sensitivity
   - Verify market is active with sufficient volatility

2. **Too many false signals**
   - Increase `min_divergence_strength`
   - Increase `lookback_period`
   - Use longer timeframe candles

3. **Strategy not starting**
   - Verify exchange connection
   - Check trading pair is available
   - Ensure sufficient balance

### Debug Information

The strategy provides detailed status information including:
- Current RSI value
- High/low point detection
- Divergence analysis
- Signal strength
- Recent candle data

## Disclaimer

This strategy is for educational purposes. Always:
- Test thoroughly before using real money
- Understand the risks involved
- Start with small amounts
- Monitor performance regularly
- Adjust parameters based on market conditions

Cryptocurrency trading involves substantial risk and may not be suitable for all investors.
