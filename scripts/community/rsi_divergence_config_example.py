"""
RSI Divergence Strategy Configuration Example

This file shows how to customize the RSI Divergence Strategy parameters.
Copy this file and modify the parameters according to your trading preferences.

Usage:
1. Copy this file to a new name (e.g., my_rsi_divergence_config.py)
2. Modify the parameters below
3. Import and use your custom configuration in the main strategy file
"""

from decimal import Decimal
from hummingbot.data_feed.candles_feed.candles_factory import CandlesFactory
from hummingbot.data_feed.candles_feed.data_types import CandlesConfig
from scripts.community.rsi_divergence_strategy import RSIDivergenceStrategy


class MyRSIDivergenceStrategy(RSIDivergenceStrategy):
    """
    Customized RSI Divergence Strategy with your preferred parameters.
    """
    
    # === BASIC TRADING PARAMETERS ===
    directional_strategy_name: str = "My_RSI_Divergence"
    trading_pair: str = "ETH-USDT"  # Change to your preferred trading pair
    exchange: str = "binance"       # Change to your preferred exchange
    order_amount_usd = Decimal("100")  # Order size in USD
    leverage = 1                    # Leverage (1 for spot trading)
    
    # === TECHNICAL ANALYSIS PARAMETERS ===
    lookback_period: int = 10       # Period for high/low point detection (5-20 recommended)
    rsi_period: int = 14           # RSI calculation period (14 is standard)
    min_divergence_strength: float = 0.02  # Minimum divergence strength (0.01-0.05 recommended)
    
    # === RISK MANAGEMENT PARAMETERS ===
    stop_loss: float = 0.015       # Stop loss percentage (1.5%)
    take_profit: float = 0.03      # Take profit percentage (3%)
    time_limit: int = 60 * 90      # Position time limit in seconds (90 minutes)
    
    # === ADVANCED RISK MANAGEMENT ===
    trailing_stop_activation_delta = 0.01   # Activate trailing stop at 1% profit
    trailing_stop_trailing_delta = 0.005    # Trail by 0.5%
    
    # === CANDLE DATA CONFIGURATION ===
    candle_interval = "5m"         # Candle interval: 1m, 3m, 5m, 15m, 30m, 1h, 4h, 1d
    max_candle_records = 500       # Number of historical candles to keep
    
    # Override candles configuration
    candles = [CandlesFactory.get_candle(CandlesConfig(
        connector=exchange, 
        trading_pair=trading_pair, 
        interval=candle_interval, 
        max_records=max_candle_records
    ))]
    markets = {exchange: {trading_pair}}


# === ALTERNATIVE CONFIGURATIONS ===

class ConservativeRSIDivergence(RSIDivergenceStrategy):
    """
    Conservative configuration with tighter risk management.
    """
    directional_strategy_name: str = "Conservative_RSI_Divergence"
    trading_pair: str = "BTC-USDT"
    exchange: str = "binance"
    order_amount_usd = Decimal("50")
    
    # More conservative parameters
    lookback_period: int = 15
    rsi_period: int = 21
    min_divergence_strength: float = 0.03  # Higher threshold
    
    # Tighter risk management
    stop_loss: float = 0.01        # 1% stop loss
    take_profit: float = 0.02      # 2% take profit
    time_limit: int = 60 * 60      # 1 hour limit
    
    candle_interval = "15m"        # Longer timeframe
    
    candles = [CandlesFactory.get_candle(CandlesConfig(
        connector=exchange, 
        trading_pair=trading_pair, 
        interval=candle_interval, 
        max_records=300
    ))]
    markets = {exchange: {trading_pair}}


class AggressiveRSIDivergence(RSIDivergenceStrategy):
    """
    Aggressive configuration for more frequent trading.
    """
    directional_strategy_name: str = "Aggressive_RSI_Divergence"
    trading_pair: str = "ETH-USDT"
    exchange: str = "binance"
    order_amount_usd = Decimal("200")
    leverage = 3  # Higher leverage for futures
    
    # More sensitive parameters
    lookback_period: int = 7
    rsi_period: int = 10
    min_divergence_strength: float = 0.015  # Lower threshold
    
    # Wider risk management for more room
    stop_loss: float = 0.02        # 2% stop loss
    take_profit: float = 0.04      # 4% take profit
    time_limit: int = 60 * 120     # 2 hours limit
    
    candle_interval = "3m"         # Shorter timeframe
    
    candles = [CandlesFactory.get_candle(CandlesConfig(
        connector=exchange, 
        trading_pair=trading_pair, 
        interval=candle_interval, 
        max_records=600
    ))]
    markets = {exchange: {trading_pair}}


class ScalpingRSIDivergence(RSIDivergenceStrategy):
    """
    Scalping configuration for very short-term trades.
    """
    directional_strategy_name: str = "Scalping_RSI_Divergence"
    trading_pair: str = "BTC-USDT"
    exchange: str = "binance"
    order_amount_usd = Decimal("300")
    leverage = 5  # Higher leverage for scalping
    
    # Fast parameters
    lookback_period: int = 5
    rsi_period: int = 7
    min_divergence_strength: float = 0.01  # Very sensitive
    
    # Quick scalping targets
    stop_loss: float = 0.005       # 0.5% stop loss
    take_profit: float = 0.01      # 1% take profit
    time_limit: int = 60 * 15      # 15 minutes limit
    
    # Very fast trailing stop
    trailing_stop_activation_delta = 0.003
    trailing_stop_trailing_delta = 0.001
    
    candle_interval = "1m"         # 1-minute candles
    
    candles = [CandlesFactory.get_candle(CandlesConfig(
        connector=exchange, 
        trading_pair=trading_pair, 
        interval=candle_interval, 
        max_records=1000
    ))]
    markets = {exchange: {trading_pair}}


# === PARAMETER GUIDELINES ===
"""
LOOKBACK_PERIOD:
- Smaller values (5-8): More sensitive, more signals, more false positives
- Medium values (10-15): Balanced approach, good for most markets
- Larger values (20-30): Less sensitive, fewer but more reliable signals

RSI_PERIOD:
- Standard: 14 (most common)
- Faster: 7-10 (more responsive to price changes)
- Slower: 21-30 (smoother, less noise)

MIN_DIVERGENCE_STRENGTH:
- Conservative: 0.03-0.05 (fewer but stronger signals)
- Balanced: 0.02-0.03 (good balance)
- Aggressive: 0.01-0.02 (more signals, higher risk)

CANDLE_INTERVALS:
- Scalping: 1m, 3m
- Day trading: 5m, 15m
- Swing trading: 30m, 1h, 4h
- Position trading: 1d

RISK MANAGEMENT:
- Always set stop_loss < take_profit
- Consider market volatility when setting percentages
- Use trailing stops for trend-following behavior
- Adjust time_limit based on your trading style
"""
