from decimal import Decimal
import pandas as pd
import numpy as np
# Removed unused imports

from hummingbot.data_feed.candles_feed.candles_factory import CandlesFactory
from hummingbot.data_feed.candles_feed.data_types import CandlesConfig
from hummingbot.strategy.directional_strategy_base import DirectionalStrategyBase


class RSIDivergenceStrategy(DirectionalStrategyBase):
    """
    RSI Divergence Strategy implementation based on the DirectionalStrategyBase.
    
    This strategy detects divergences between price action and RSI indicator:
    - Bearish Divergence: Price makes higher highs while RSI makes lower highs (sell signal)
    - Bullish Divergence: Price makes lower lows while RSI makes higher lows (buy signal)
    
    The strategy is converted from a TradingView PineScript that detects high/low points
    and compares price ratios with RSI ratios to identify divergence patterns.
    
    Parameters:
        directional_strategy_name (str): The name of the strategy.
        trading_pair (str): The trading pair to be traded.
        exchange (str): The exchange to be used for trading.
        order_amount_usd (Decimal): The amount of the order in USD.
        leverage (int): The leverage to be used for trading.
        lookback_period (int): Period for high/low point detection (equivalent to 'p' in PineScript).
        rsi_period (int): Period for RSI calculation.
        min_divergence_strength (float): Minimum strength required for divergence signal.
    
    Position Parameters:
        stop_loss (float): The stop-loss percentage for the position.
        take_profit (float): The take-profit percentage for the position.
        time_limit (int): The time limit for the position in seconds.
    """
    
    directional_strategy_name: str = "RSI_Divergence"
    
    # Define the trading pair and exchange
    trading_pair: str = "BTC-USDT"
    exchange: str = "binance"
    order_amount_usd = Decimal("50")
    leverage = 1
    
    # Strategy specific parameters
    lookback_period: int = 10  # Equivalent to 'p' parameter in PineScript
    rsi_period: int = 14
    min_divergence_strength: float = 0.02  # Minimum 2% divergence strength
    
    # Configure the parameters for the position
    stop_loss: float = 0.01  # 1%
    take_profit: float = 0.02  # 2%
    time_limit: int = 60 * 60  # 1 hour
    trailing_stop_activation_delta = 0.005
    trailing_stop_trailing_delta = 0.002
    
    # Candles configuration - using 5m intervals for better signal detection
    candles = [CandlesFactory.get_candle(CandlesConfig(
        connector=exchange, 
        trading_pair=trading_pair, 
        interval="5m", 
        max_records=500
    ))]
    markets = {exchange: {trading_pair}}
    
    def __init__(self):
        super().__init__()
        self.last_high_time = 0
        self.last_low_time = 0
        self.last_high2_time = 0
        self.last_low2_time = 0
        self.current_signal = 0
        self.divergence_info = ""
        
    def get_signal(self):
        """
        Generates the trading signal based on RSI divergence detection.
        Returns:
            int: The trading signal (-1 for sell, 0 for hold, 1 for buy).
        """
        try:
            candles_df = self.get_processed_df()
            if len(candles_df) < self.lookback_period * 3:
                self.divergence_info = "Insufficient data for analysis"
                return 0

            signal = self._detect_divergence(candles_df)

            # Calculate divergence strength for additional validation
            strength = self.get_divergence_strength(candles_df)

            # Only trade if divergence strength is above minimum threshold
            if signal != 0 and strength < self.min_divergence_strength:
                self.divergence_info += f" (Strength: {strength:.3f} - Below threshold)"
                signal = 0
            elif signal != 0:
                self.divergence_info += f" (Strength: {strength:.3f})"
                self._log_divergence_details(candles_df, signal)

            self.current_signal = signal
            return signal

        except Exception as e:
            self.logger().error(f"Error in get_signal: {e}")
            import traceback
            self.logger().error(f"Traceback: {traceback.format_exc()}")
            self.divergence_info = f"Error: {str(e)}"
            return 0
    
    def get_processed_df(self):
        """
        Retrieves the processed dataframe with RSI values and high/low point detection.
        Returns:
            pd.DataFrame: The processed dataframe with technical indicators.
        """
        candles_df = self.candles[0].candles_df.copy()
        
        # Calculate RSI
        candles_df.ta.rsi(length=self.rsi_period, append=True)
        
        # Add high/low point detection
        candles_df = self._add_high_low_points(candles_df)
        
        return candles_df
    
    def _add_high_low_points(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add high and low point detection to the dataframe.
        More accurate implementation equivalent to PineScript's ta.highestbars and ta.lowestbars.
        """
        df = df.copy()

        # Initialize columns
        df['is_high'] = False
        df['is_low'] = False
        df['high_bars_back'] = np.nan
        df['low_bars_back'] = np.nan

        # Calculate highest/lowest bars back for each position
        for i in range(self.lookback_period, len(df)):
            # Get the lookback window
            window_high = df['high'].iloc[i-self.lookback_period:i+1]
            window_low = df['low'].iloc[i-self.lookback_period:i+1]

            # Find the position of highest/lowest values (bars back from current)
            highest_pos = window_high.idxmax()
            lowest_pos = window_low.idxmin()

            # Calculate bars back (0 means current bar is the highest/lowest)
            high_bars_back = i - highest_pos
            low_bars_back = i - lowest_pos

            df.loc[df.index[i], 'high_bars_back'] = high_bars_back
            df.loc[df.index[i], 'low_bars_back'] = low_bars_back

            # Mark as high/low point if current bar is the extreme within lookback period
            if high_bars_back == 0:
                df.loc[df.index[i], 'is_high'] = True
            if low_bars_back == 0:
                df.loc[df.index[i], 'is_low'] = True

        return df
    
    def _detect_divergence(self, df: pd.DataFrame) -> int:
        """
        Detect RSI divergence patterns following PineScript logic more closely.
        Returns:
            int: 1 for bullish divergence, -1 for bearish divergence, 0 for no signal
        """
        if len(df) < self.lookback_period * 2:
            return 0

        rsi_col = f"RSI_{self.rsi_period}"
        current_idx = len(df) - 1

        # Get current high/low bars back values (equivalent to h, h2, l, l2 in PineScript)
        current_high_bars_back = df['high_bars_back'].iloc[-1]
        current_low_bars_back = df['low_bars_back'].iloc[-1]

        # Calculate previous period's high/low bars back (equivalent to h2, l2)
        if current_idx >= self.lookback_period:
            prev_high_bars_back = df['high_bars_back'].iloc[current_idx - self.lookback_period]
            prev_low_bars_back = df['low_bars_back'].iloc[current_idx - self.lookback_period]
        else:
            return 0

        # Check for bearish divergence (equivalent to PineScript condition: hx > 1 and hx_rsi < 1)
        if not pd.isna(current_high_bars_back) and not pd.isna(prev_high_bars_back):
            if current_high_bars_back != prev_high_bars_back:
                # Get the actual high points
                h_idx = current_idx - int(current_high_bars_back)
                h2_idx = current_idx - self.lookback_period - int(prev_high_bars_back)

                if h_idx >= 0 and h2_idx >= 0 and h_idx < len(df) and h2_idx < len(df):
                    # Calculate price and RSI ratios (equivalent to hx and hx_rsi)
                    price_ratio = df['high'].iloc[h_idx] / df['high'].iloc[h2_idx]
                    rsi_ratio = df[rsi_col].iloc[h_idx] / df[rsi_col].iloc[h2_idx]

                    # Bearish divergence: price higher high (>1) but RSI lower high (<1)
                    if price_ratio > 1.0 and rsi_ratio < 1.0:
                        # Check if this is a new signal (time-based filtering)
                        h_time = df['timestamp'].iloc[h_idx] if 'timestamp' in df.columns else h_idx
                        h2_time = df['timestamp'].iloc[h2_idx] if 'timestamp' in df.columns else h2_idx

                        if h_time != self.last_high_time and h2_time != self.last_high2_time:
                            self.last_high_time = h_time
                            self.last_high2_time = h2_time
                            self.divergence_info = f"Bearish divergence: Price {price_ratio:.3f}, RSI {rsi_ratio:.3f}"
                            return -1

        # Check for bullish divergence (equivalent to PineScript condition: lx < 1 and lx_rsi > 1)
        if not pd.isna(current_low_bars_back) and not pd.isna(prev_low_bars_back):
            if current_low_bars_back != prev_low_bars_back:
                # Get the actual low points
                l_idx = current_idx - int(current_low_bars_back)
                l2_idx = current_idx - self.lookback_period - int(prev_low_bars_back)

                if l_idx >= 0 and l2_idx >= 0 and l_idx < len(df) and l2_idx < len(df):
                    # Calculate price and RSI ratios (equivalent to lx and lx_rsi)
                    price_ratio = df['low'].iloc[l_idx] / df['low'].iloc[l2_idx]
                    rsi_ratio = df[rsi_col].iloc[l_idx] / df[rsi_col].iloc[l2_idx]

                    # Bullish divergence: price lower low (<1) but RSI higher low (>1)
                    if price_ratio < 1.0 and rsi_ratio > 1.0:
                        # Check if this is a new signal (time-based filtering)
                        l_time = df['timestamp'].iloc[l_idx] if 'timestamp' in df.columns else l_idx
                        l2_time = df['timestamp'].iloc[l2_idx] if 'timestamp' in df.columns else l2_idx

                        if l_time != self.last_low_time and l2_time != self.last_low2_time:
                            self.last_low_time = l_time
                            self.last_low2_time = l2_time
                            self.divergence_info = f"Bullish divergence: Price {price_ratio:.3f}, RSI {rsi_ratio:.3f}"
                            return 1

        self.divergence_info = "No divergence detected"
        return 0
    
    def market_data_extra_info(self):
        """
        Provides additional information about the market data and strategy status.
        Returns:
            List[str]: A list of formatted strings containing market data information.
        """
        lines = []

        try:
            candles_df = self.get_processed_df()
            if len(candles_df) == 0:
                return ["No candle data available"]

            # Strategy header
            lines.extend([f"\n=== RSI Divergence Strategy ==="])
            lines.extend([f"Pair: {self.trading_pair} | Exchange: {self.exchange}"])
            lines.extend([f"Interval: {self.candles[0].interval} | Lookback: {self.lookback_period} | RSI Period: {self.rsi_period}"])

            # Current market data
            rsi_col = f"RSI_{self.rsi_period}"
            if rsi_col in candles_df.columns and len(candles_df) > 0:
                current_rsi = candles_df[rsi_col].iloc[-1]
                current_price = candles_df['close'].iloc[-1]
                current_high_bars = candles_df['high_bars_back'].iloc[-1]
                current_low_bars = candles_df['low_bars_back'].iloc[-1]

                lines.extend([f"\nCurrent Market Data:"])
                lines.extend([f"  Price: {current_price:.4f}"])
                lines.extend([f"  RSI: {current_rsi:.2f}"])
                lines.extend([f"  High bars back: {current_high_bars if not pd.isna(current_high_bars) else 'N/A'}"])
                lines.extend([f"  Low bars back: {current_low_bars if not pd.isna(current_low_bars) else 'N/A'}"])

            # Signal status
            signal_text = "HOLD"
            signal_color = "🟡"
            if self.current_signal == 1:
                signal_text = "BUY"
                signal_color = "🟢"
            elif self.current_signal == -1:
                signal_text = "SELL"
                signal_color = "🔴"

            lines.extend([f"\nSignal Status: {signal_color} {signal_text}"])
            lines.extend([f"Divergence: {self.divergence_info}"])

            # High/Low points analysis
            high_points = candles_df[candles_df['is_high'] == True]
            low_points = candles_df[candles_df['is_low'] == True]

            lines.extend([f"\nHigh/Low Points Analysis:"])
            lines.extend([f"  Total High Points: {len(high_points)}"])
            lines.extend([f"  Total Low Points: {len(low_points)}"])

            if len(high_points) >= 2:
                recent_highs = high_points.tail(2)
                h1_price = recent_highs['high'].iloc[0]
                h2_price = recent_highs['high'].iloc[1]
                h1_rsi = recent_highs[rsi_col].iloc[0]
                h2_rsi = recent_highs[rsi_col].iloc[1]
                lines.extend([f"  Last 2 Highs: {h1_price:.4f} (RSI: {h1_rsi:.2f}) -> {h2_price:.4f} (RSI: {h2_rsi:.2f})"])

            if len(low_points) >= 2:
                recent_lows = low_points.tail(2)
                l1_price = recent_lows['low'].iloc[0]
                l2_price = recent_lows['low'].iloc[1]
                l1_rsi = recent_lows[rsi_col].iloc[0]
                l2_rsi = recent_lows[rsi_col].iloc[1]
                lines.extend([f"  Last 2 Lows: {l1_price:.4f} (RSI: {l1_rsi:.2f}) -> {l2_price:.4f} (RSI: {l2_rsi:.2f})"])

            # Recent candles summary
            lines.extend([f"\nRecent Candles Summary:"])
            recent_candles = candles_df.tail(3)
            for idx, row in recent_candles.iterrows():
                timestamp = row.get('timestamp', idx)
                high_marker = " 📈" if row.get('is_high', False) else ""
                low_marker = " 📉" if row.get('is_low', False) else ""
                lines.extend([f"  {timestamp}: Close {row['close']:.4f}, RSI {row[rsi_col]:.2f}{high_marker}{low_marker}"])

        except Exception as e:
            lines.extend([f"Error in market_data_extra_info: {e}"])
            import traceback
            lines.extend([f"Traceback: {traceback.format_exc()}"])

        return lines

    def _log_divergence_details(self, df: pd.DataFrame, signal: int):
        """
        Log detailed information about detected divergences for debugging.
        """
        if signal == 0:
            return

        rsi_col = f"RSI_{self.rsi_period}"
        signal_type = "Bullish" if signal == 1 else "Bearish"

        self.logger().info(f"=== {signal_type} Divergence Detected ===")
        self.logger().info(f"Signal: {signal}")
        self.logger().info(f"Divergence Info: {self.divergence_info}")

        # Log recent high/low points for context
        if signal == -1:  # Bearish divergence
            high_points = df[df['is_high'] == True].tail(2)
            if len(high_points) >= 2:
                for i, (_, row) in enumerate(high_points.iterrows()):
                    self.logger().info(f"High Point {i+1}: Price {row['high']:.4f}, RSI {row[rsi_col]:.2f}")

        elif signal == 1:  # Bullish divergence
            low_points = df[df['is_low'] == True].tail(2)
            if len(low_points) >= 2:
                for i, (_, row) in enumerate(low_points.iterrows()):
                    self.logger().info(f"Low Point {i+1}: Price {row['low']:.4f}, RSI {row[rsi_col]:.2f}")

    def get_divergence_strength(self, df: pd.DataFrame) -> float:
        """
        Calculate the strength of the current divergence signal.
        Returns a value between 0 and 1, where 1 is the strongest divergence.
        """
        if len(df) < self.lookback_period * 2:
            return 0.0

        rsi_col = f"RSI_{self.rsi_period}"
        current_idx = len(df) - 1

        current_high_bars_back = df['high_bars_back'].iloc[-1]
        current_low_bars_back = df['low_bars_back'].iloc[-1]

        if current_idx >= self.lookback_period:
            prev_high_bars_back = df['high_bars_back'].iloc[current_idx - self.lookback_period]
            prev_low_bars_back = df['low_bars_back'].iloc[current_idx - self.lookback_period]
        else:
            return 0.0

        max_strength = 0.0

        # Check bearish divergence strength
        if not pd.isna(current_high_bars_back) and not pd.isna(prev_high_bars_back):
            if current_high_bars_back != prev_high_bars_back:
                h_idx = current_idx - int(current_high_bars_back)
                h2_idx = current_idx - self.lookback_period - int(prev_high_bars_back)

                if h_idx >= 0 and h2_idx >= 0 and h_idx < len(df) and h2_idx < len(df):
                    price_ratio = df['high'].iloc[h_idx] / df['high'].iloc[h2_idx]
                    rsi_ratio = df[rsi_col].iloc[h_idx] / df[rsi_col].iloc[h2_idx]

                    if price_ratio > 1.0 and rsi_ratio < 1.0:
                        # Strength based on how much price went up vs how much RSI went down
                        price_change = (price_ratio - 1.0) * 100  # Percentage increase
                        rsi_change = (1.0 - rsi_ratio) * 100     # Percentage decrease
                        strength = min(price_change + rsi_change, 100) / 100
                        max_strength = max(max_strength, strength)

        # Check bullish divergence strength
        if not pd.isna(current_low_bars_back) and not pd.isna(prev_low_bars_back):
            if current_low_bars_back != prev_low_bars_back:
                l_idx = current_idx - int(current_low_bars_back)
                l2_idx = current_idx - self.lookback_period - int(prev_low_bars_back)

                if l_idx >= 0 and l2_idx >= 0 and l_idx < len(df) and l2_idx < len(df):
                    price_ratio = df['low'].iloc[l_idx] / df['low'].iloc[l2_idx]
                    rsi_ratio = df[rsi_col].iloc[l_idx] / df[rsi_col].iloc[l2_idx]

                    if price_ratio < 1.0 and rsi_ratio > 1.0:
                        # Strength based on how much price went down vs how much RSI went up
                        price_change = (1.0 - price_ratio) * 100  # Percentage decrease
                        rsi_change = (rsi_ratio - 1.0) * 100      # Percentage increase
                        strength = min(price_change + rsi_change, 100) / 100
                        max_strength = max(max_strength, strength)

        return max_strength
